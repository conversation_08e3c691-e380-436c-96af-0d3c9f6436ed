<script setup lang="ts">
defineOptions({
  name: 'Account'
})

const data = ref([
  {
    id: '1',
    platform: '微信',
    logo: '/weixin.svg',
    shop: {
      url: 'https://channels.weixin.qq.com/shop/home',
      username: '悠然见南山',
      cookie: '666',
      status: false,
    },
    helper: {
      url: 'https://channels.weixin.qq.com/login.html',
      username: undefined,
      cookie: undefined,
      status: true,
    },
    planList: [
      { value: 1, label: '计划1' },
      { value: 2, label: '计划2' },
      { value: 3, label: '计划3' },
    ],
    playIndex: undefined,
    sync: false,
    status: true,
  },
  {
    id: '2',
    platform: '抖音',
    logo: '/douyin.svg',
    shop: {
      url: 'https://fxg.jinritemai.com/login/common',
      username: undefined,
      cookie: undefined,
      status: true,
    },
    helper: {
      url: 'https://buyin.jinritemai.com/mpa/account/login',
      username: undefined,
      cookie: undefined,
      status: true,
    },
    planList: [],
    playIndex: undefined,
    sync: false,
    status: true,
  },
  {
    id: '3',
    platform: '快手',
    logo: '/kuaishou.png',
    shop: {
      url: 'https://login.kwaixiaodian.com',
      username: undefined,
      cookie: undefined,
      status: true,
    },
    helper: {
      url: 'https://fxg.jinritemai.com/login/common',
      username: undefined,
      cookie: undefined,
      status: true,
    },
    planList: [],
    playIndex: undefined,
    sync: false,
    status: true,
  },
  {
    id: '4',
    platform: '小红书',
    logo: '/xiaohongshu.png',
    shop: {
      url: 'https://zhaoshang.xiaohongshu.com/merchant/login',
      username: undefined,
      cookie: undefined,
      status: true,
    },
    helper: {
      url: 'https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/app-system/home',
      username: undefined,
      cookie: undefined,
      status: true,
    },
    planList: [],
    playIndex: undefined,
    sync: false,
    status: true,
  }
])

const handleChange = (_data: any) => {
  console.log(_data);
  data.value = _data
}

const handleShopLogin = (record: any) => {
  console.log(record);
}

const handleHeplerLogin = (record: any) => {
  console.log(record);
}

const refreshPlan = (record: any) => {
  console.log(record);
}
</script>

<template>
  <a-table row-key="id" :bordered="{ cell: true }" :scroll="{ minWidth: 800 }" :data="data" size="small" :pagination="false" @change="handleChange" :draggable="{ type: 'handle', width: 40 }">
    <template #columns>
      <a-table-column title="直播平台" :width="120" align="center">
        <template #cell="{ record }">
          <img :src="record.logo" alt="" class="inline-flex h-20px" />
        </template>
      </a-table-column>
      <a-table-column title="小店状态" :width="180" align="center">
        <template #cell="{ record }">
          <div class="inline-flex items-center gap-x-4px">
            <a-tag v-if="record.shop.cookie" class="w-80px justify-center cursor-pointer" size="small" color="green" nowrap @click="handleShopLogin(record)">{{  record.shop.username }}</a-tag>
            <a-button v-else type="text" size="mini" @click="handleShopLogin(record)">登录</a-button>
            <a-tag v-if="!record.shop.status" size="small" color="red">已封禁</a-tag>
          </div>
        </template>
      </a-table-column>
      <a-table-column title="助手状态" :width="180" align="center">
        <template #cell="{ record }">
          <div class="inline-flex items-center gap-x-4px">
            <a-tag v-if="record.helper.cookie" class="w-80px justify-center cursor-pointer" size="small" color="green" nowrap @click="handleShopLogin(record)">{{  record.helper.username }}</a-tag>
            <a-button v-else type="text" size="mini" @click="handleShopLogin(record)">登录</a-button>
            <a-tag v-if="!record.helper.status" size="small" color="red">已封禁</a-tag>
          </div>
        </template>
      </a-table-column>
      <a-table-column title="直播计划" :width="180" align="center">
        <template #cell="{ record }">
          <div class="inline-flex items-center gap-x-8px">
            <a-select :style="{width:'140px'}" :options="record.planList" placeholder="请选择 ..." size="mini">
              <template #empty>
                <a-empty>
                  <span class="text-12px">暂无可选计划</span>
                  <div class="flex justify-center m-4px m-b-0">
                    <a-button type="text" size="mini" @click="refreshPlan">
                      <span>点击刷新</span>
                    </a-button>
                  </div>
                </a-empty>
              </template>
              <template #footer>
                <div class="flex justify-center m-4px m-b-0">
                  <a-button type="text" size="mini" long>
                    <span>点击刷新</span>
                  </a-button>
                </div>
              </template>
            </a-select>
          </div>
        </template>
      </a-table-column>
      <a-table-column title="同步状态" :width="110" align="center" fixed="right">
        <template #cell="{ record }">
          <div class="inline-flex items-center gap-x-4px">
            <a-tag v-if="record.sync" size="small" color="green">已同步</a-tag>
            <a-tag v-else size="small" color="orange">未同步</a-tag>
            <a-button type="text" size="mini" title="点击同步" @click="handleHeplerLogin(record)">
              <template #icon>
                <icon-sync :spin="false" />
              </template>
            </a-button>
          </div>
        </template>
      </a-table-column>
      <a-table-column title="启用状态" :width="90" align="center" fixed="right">
        <template #cell="{ record }">
          <a-switch v-model="record.status" type="line" size="small" />
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<style scoped lang="less"></style>