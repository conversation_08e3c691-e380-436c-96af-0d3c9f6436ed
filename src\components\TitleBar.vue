<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { getName } from '@tauri-apps/api/app'
import { getCurrentWindow } from '@tauri-apps/api/window'
import { useDarkToggle } from '@/composables/useDarkToggle'

defineOptions({
  name: 'TitleBar'
})

const appWindow = getCurrentWindow()
const isMaximized = ref(false)
const appName = ref() // 默认值，防止加载时闪烁

// 深色模式切换
const { toggleDark, isDark } = useDarkToggle()

// 窗口控制函数
const minimizeWindow = () => {
  appWindow.minimize()
}

const toggleMaximize = () => {
  appWindow.toggleMaximize()
}

const closeWindow = () => {
  appWindow.close()
}

// 监听窗口状态变化
let windowResize: (() => void) | null = null

onMounted(async () => {
  // 获取应用名称
  appName.value = await getName()

  // 获取初始状态
  isMaximized.value = await appWindow.isMaximized()

  // 监听窗口大小变化
  windowResize = await appWindow.onResized(async () => {
    isMaximized.value = await appWindow.isMaximized()
  })
})

onUnmounted(() => {
  if (windowResize) {
    try {
      windowResize()
    } catch (error) {
      console.warn('Failed to unregister window resize listener:', error)
    }
  }
})

// 全局设置
const globalSetting = () => {
  console.log('globalSetting')
}
</script>

<template>
  <div data-tauri-drag-region class="titlebar flex justify-between items-center">
    <!-- 应用标题 -->
    <div class="left flex items-center text-14px font-medium m-l-12px gap-x-8px">
      <img class="w-20px h-20px flex-shrink-0 pointer-events-none" src="/icon.png" />
      <span class="select-none">{{ appName }}</span>
    </div>

    <!-- 中间插槽 -->
    <div class="center">
      <slot></slot>
    </div>

    <!-- 窗口控制按钮 -->
    <a-space class="right" align="center" :size="0">
      <!-- 自定义按钮 -->
      <slot name="right"></slot>

      <a-button :title="isDark ? '切换浅色模式' : '切换深色模式'" @click="toggleDark">
        <icon-moon v-if="isDark" />
        <icon-sun v-else />
      </a-button>

      <a-button title="全局设置" @click="globalSetting">
        <icon-settings />
      </a-button>

      <a-button @click="minimizeWindow" title="最小化">
        <icon-minus />
      </a-button>
      <a-button @click="toggleMaximize" :title="isMaximized ? '还原' : '最大化'">
        <icon-fullscreen-exit v-if="isMaximized" />
        <icon-fullscreen v-else />
      </a-button>
      <a-button class="close" @click="closeWindow" title="关闭">
        <icon-close />
      </a-button>
    </a-space>
  </div>
</template>

<style scoped lang="less">
.titlebar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .right {
    .arco-btn {
      width: 44px;
      height: 36px;
      color: white;
      border-radius: 0;
      border: none;
      background: transparent;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      &.close:hover {
        background: #e74c3c;
        color: white;
      }

      .arco-icon {
        pointer-events: none;
      }
    }
  }
}

// 为页面内容添加顶部边距，避免被标题栏遮挡
:global(.page) {
  padding-top: 36px;
}
</style>